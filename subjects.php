<?php
require_once 'config.php';

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = sanitize_input($_POST['name']);
                $code = sanitize_input($_POST['code']);
                $credit_hours = (int)$_POST['credit_hours'];
                $department = sanitize_input($_POST['department']);
                $description = sanitize_input($_POST['description']);
                
                $query = "INSERT INTO subjects (name, code, credit_hours, department, description) VALUES (?, ?, ?, ?, ?)";
                $stmt = $db->prepare($query);
                
                if ($stmt->execute([$name, $code, $credit_hours, $department, $description])) {
                    $message = "تم إضافة المادة بنجاح";
                    $message_type = 'success';
                } else {
                    $message = "حدث خطأ في إضافة المادة";
                    $message_type = 'error';
                }
                break;
                
            case 'edit':
                $id = (int)$_POST['id'];
                $name = sanitize_input($_POST['name']);
                $code = sanitize_input($_POST['code']);
                $credit_hours = (int)$_POST['credit_hours'];
                $department = sanitize_input($_POST['department']);
                $description = sanitize_input($_POST['description']);
                
                $query = "UPDATE subjects SET name = ?, code = ?, credit_hours = ?, department = ?, description = ? WHERE id = ?";
                $stmt = $db->prepare($query);
                
                if ($stmt->execute([$name, $code, $credit_hours, $department, $description, $id])) {
                    $message = "تم تحديث المادة بنجاح";
                    $message_type = 'success';
                } else {
                    $message = "حدث خطأ في تحديث المادة";
                    $message_type = 'error';
                }
                break;
                
            case 'delete':
                $id = (int)$_POST['id'];
                
                // التحقق من وجود محاضرات مرتبطة بالمادة
                $check_query = "SELECT COUNT(*) FROM lectures WHERE subject_id = ?";
                $check_stmt = $db->prepare($check_query);
                $check_stmt->execute([$id]);
                $lecture_count = $check_stmt->fetchColumn();
                
                if ($lecture_count > 0) {
                    $message = "لا يمكن حذف المادة لأنها مرتبطة بـ $lecture_count محاضرة";
                    $message_type = 'error';
                } else {
                    $query = "DELETE FROM subjects WHERE id = ?";
                    $stmt = $db->prepare($query);
                    
                    if ($stmt->execute([$id])) {
                        $message = "تم حذف المادة بنجاح";
                        $message_type = 'success';
                    } else {
                        $message = "حدث خطأ في حذف المادة";
                        $message_type = 'error';
                    }
                }
                break;
        }
    }
}

// جلب جميع المواد
$query = "SELECT * FROM subjects ORDER BY name ASC";
$stmt = $db->prepare($query);
$stmt->execute();
$subjects = $stmt->fetchAll();

// جلب بيانات المادة للتعديل
$edit_subject = null;
if (isset($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    $query = "SELECT * FROM subjects WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$edit_id]);
    $edit_subject = $stmt->fetch();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المواد - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calendar-alt me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="lectures.php">
                            <i class="fas fa-chalkboard-teacher me-1"></i>المحاضرات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="halls.php">
                            <i class="fas fa-building me-1"></i>القاعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="professors.php">
                            <i class="fas fa-user-tie me-1"></i>الأساتذة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.php">
                            <i class="fas fa-book me-1"></i>المواد
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <?php echo show_message($message, $message_type); ?>
        <?php endif; ?>

        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center">
                    <i class="fas fa-book text-primary me-2"></i>
                    إدارة المواد الدراسية
                </h1>
            </div>
        </div>

        <div class="row">
            <!-- نموذج إضافة/تعديل المادة -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-plus me-2"></i>
                            <?php echo $edit_subject ? 'تعديل المادة' : 'إضافة مادة جديدة'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="<?php echo $edit_subject ? 'edit' : 'add'; ?>">
                            <?php if ($edit_subject): ?>
                                <input type="hidden" name="id" value="<?php echo $edit_subject['id']; ?>">
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم المادة</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo $edit_subject ? $edit_subject['name'] : ''; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="code" class="form-label">رمز المادة</label>
                                <input type="text" class="form-control" id="code" name="code" 
                                       value="<?php echo $edit_subject ? $edit_subject['code'] : ''; ?>" 
                                       placeholder="مثال: CS101" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="credit_hours" class="form-label">عدد الساعات المعتمدة</label>
                                <input type="number" class="form-control" id="credit_hours" name="credit_hours" 
                                       value="<?php echo $edit_subject ? $edit_subject['credit_hours'] : '3'; ?>" 
                                       min="1" max="6" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="department" class="form-label">القسم</label>
                                <input type="text" class="form-control" id="department" name="department" 
                                       value="<?php echo $edit_subject ? $edit_subject['department'] : ''; ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف المادة</label>
                                <textarea class="form-control" id="description" name="description" rows="3"><?php echo $edit_subject ? $edit_subject['description'] : ''; ?></textarea>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    <?php echo $edit_subject ? 'تحديث المادة' : 'إضافة المادة'; ?>
                                </button>
                                <?php if ($edit_subject): ?>
                                    <a href="subjects.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة المواد -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة المواد الدراسية (<?php echo count($subjects); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($subjects)): ?>
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد مواد مسجلة
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>اسم المادة</th>
                                            <th>الرمز</th>
                                            <th>الساعات</th>
                                            <th>القسم</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($subjects as $subject): ?>
                                            <tr>
                                                <td><strong><?php echo $subject['name']; ?></strong></td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo $subject['code']; ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $subject['credit_hours']; ?> ساعة</span>
                                                </td>
                                                <td><?php echo $subject['department'] ?: '-'; ?></td>
                                                <td>
                                                    <a href="subjects.php?edit=<?php echo $subject['id']; ?>" class="btn btn-sm btn-warning me-1">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger me-1" 
                                                            onclick="confirmDelete(<?php echo $subject['id']; ?>, '<?php echo $subject['name']; ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-info" 
                                                            onclick="showDetails(<?php echo $subject['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج حذف المادة -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف المادة <strong id="subjectNameToDelete"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" id="subjectIdToDelete">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج عرض تفاصيل المادة -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">تفاصيل المادة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="subjectDetails">
                    <!-- سيتم ملء التفاصيل هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(id, name) {
            document.getElementById('subjectIdToDelete').value = id;
            document.getElementById('subjectNameToDelete').textContent = name;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function showDetails(id) {
            // جلب تفاصيل المادة
            fetch('get_subject_details.php?id=' + id)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('subjectDetails').innerHTML = data;
                    new bootstrap.Modal(document.getElementById('detailsModal')).show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في جلب التفاصيل');
                });
        }
    </script>
</body>
</html>
