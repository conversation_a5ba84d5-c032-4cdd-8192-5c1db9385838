<?php
require_once 'config.php';

// البحث عن التعارضات في الجدولة
function findConflicts($db) {
    $conflicts = [];
    
    // البحث عن تعارضات القاعات
    $hall_conflicts_query = "
        SELECT l1.id as lecture1_id, l1.title as lecture1_title, l1.start_time as start1, l1.end_time as end1,
               l2.id as lecture2_id, l2.title as lecture2_title, l2.start_time as start2, l2.end_time as end2,
               h.name as hall_name, l1.day_of_week,
               s1.name as subject1_name, s2.name as subject2_name,
               p1.name as professor1_name, p2.name as professor2_name
        FROM lectures l1
        JOIN lectures l2 ON l1.hall_id = l2.hall_id AND l1.id < l2.id
        JOIN halls h ON l1.hall_id = h.id
        JOIN subjects s1 ON l1.subject_id = s1.id
        JOIN subjects s2 ON l2.subject_id = s2.id
        JOIN professors p1 ON l1.professor_id = p1.id
        JOIN professors p2 ON l2.professor_id = p2.id
        WHERE l1.day_of_week = l2.day_of_week 
        AND l1.status = 'مجدولة' AND l2.status = 'مجدولة'
        AND ((l1.start_time <= l2.start_time AND l1.end_time > l2.start_time) 
             OR (l1.start_time < l2.end_time AND l1.end_time >= l2.end_time)
             OR (l2.start_time <= l1.start_time AND l2.end_time > l1.start_time))
        ORDER BY l1.day_of_week, l1.start_time
    ";
    
    $stmt = $db->prepare($hall_conflicts_query);
    $stmt->execute();
    $hall_conflicts = $stmt->fetchAll();
    
    foreach ($hall_conflicts as $conflict) {
        $conflicts[] = [
            'type' => 'قاعة',
            'resource' => $conflict['hall_name'],
            'day' => $conflict['day_of_week'],
            'lecture1' => [
                'id' => $conflict['lecture1_id'],
                'title' => $conflict['lecture1_title'],
                'subject' => $conflict['subject1_name'],
                'professor' => $conflict['professor1_name'],
                'time' => format_time($conflict['start1']) . ' - ' . format_time($conflict['end1'])
            ],
            'lecture2' => [
                'id' => $conflict['lecture2_id'],
                'title' => $conflict['lecture2_title'],
                'subject' => $conflict['subject2_name'],
                'professor' => $conflict['professor2_name'],
                'time' => format_time($conflict['start2']) . ' - ' . format_time($conflict['end2'])
            ]
        ];
    }
    
    // البحث عن تعارضات الأساتذة
    $professor_conflicts_query = "
        SELECT l1.id as lecture1_id, l1.title as lecture1_title, l1.start_time as start1, l1.end_time as end1,
               l2.id as lecture2_id, l2.title as lecture2_title, l2.start_time as start2, l2.end_time as end2,
               p.name as professor_name, l1.day_of_week,
               s1.name as subject1_name, s2.name as subject2_name,
               h1.name as hall1_name, h2.name as hall2_name
        FROM lectures l1
        JOIN lectures l2 ON l1.professor_id = l2.professor_id AND l1.id < l2.id
        JOIN professors p ON l1.professor_id = p.id
        JOIN subjects s1 ON l1.subject_id = s1.id
        JOIN subjects s2 ON l2.subject_id = s2.id
        JOIN halls h1 ON l1.hall_id = h1.id
        JOIN halls h2 ON l2.hall_id = h2.id
        WHERE l1.day_of_week = l2.day_of_week 
        AND l1.status = 'مجدولة' AND l2.status = 'مجدولة'
        AND ((l1.start_time <= l2.start_time AND l1.end_time > l2.start_time) 
             OR (l1.start_time < l2.end_time AND l1.end_time >= l2.end_time)
             OR (l2.start_time <= l1.start_time AND l2.end_time > l1.start_time))
        ORDER BY l1.day_of_week, l1.start_time
    ";
    
    $stmt = $db->prepare($professor_conflicts_query);
    $stmt->execute();
    $professor_conflicts = $stmt->fetchAll();
    
    foreach ($professor_conflicts as $conflict) {
        $conflicts[] = [
            'type' => 'أستاذ',
            'resource' => $conflict['professor_name'],
            'day' => $conflict['day_of_week'],
            'lecture1' => [
                'id' => $conflict['lecture1_id'],
                'title' => $conflict['lecture1_title'],
                'subject' => $conflict['subject1_name'],
                'hall' => $conflict['hall1_name'],
                'time' => format_time($conflict['start1']) . ' - ' . format_time($conflict['end1'])
            ],
            'lecture2' => [
                'id' => $conflict['lecture2_id'],
                'title' => $conflict['lecture2_title'],
                'subject' => $conflict['subject2_name'],
                'hall' => $conflict['hall2_name'],
                'time' => format_time($conflict['start2']) . ' - ' . format_time($conflict['end2'])
            ]
        ];
    }
    
    return $conflicts;
}

$conflicts = findConflicts($db);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص التعارضات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calendar-alt me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="lectures.php">
                            <i class="fas fa-chalkboard-teacher me-1"></i>المحاضرات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="halls.php">
                            <i class="fas fa-building me-1"></i>القاعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="professors.php">
                            <i class="fas fa-user-tie me-1"></i>الأساتذة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-1"></i>المواد
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    فحص التعارضات في الجدولة
                </h1>
            </div>
        </div>

        <!-- نتائج فحص التعارضات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header <?php echo empty($conflicts) ? 'bg-success' : 'bg-danger'; ?> text-white">
                        <h5 class="mb-0">
                            <i class="fas <?php echo empty($conflicts) ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> me-2"></i>
                            نتائج الفحص
                            <?php if (!empty($conflicts)): ?>
                                <span class="badge bg-light text-dark ms-2"><?php echo count($conflicts); ?> تعارض</span>
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($conflicts)): ?>
                            <div class="alert alert-success text-center">
                                <i class="fas fa-check-circle fa-3x mb-3"></i>
                                <h4>ممتاز! لا توجد تعارضات في الجدولة</h4>
                                <p class="mb-0">جميع المحاضرات مجدولة بشكل صحيح بدون تعارضات</p>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تم العثور على <?php echo count($conflicts); ?> تعارض في الجدولة!</strong>
                                يرجى مراجعة التعارضات أدناه وحلها.
                            </div>
                            
                            <?php foreach ($conflicts as $index => $conflict): ?>
                                <div class="card mb-3 border-danger">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0 text-danger">
                                            <i class="fas fa-exclamation-circle me-2"></i>
                                            تعارض #<?php echo $index + 1; ?> - تعارض في <?php echo $conflict['type']; ?>: <?php echo $conflict['resource']; ?>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="card border-warning">
                                                    <div class="card-header bg-warning text-dark">
                                                        <strong>المحاضرة الأولى</strong>
                                                    </div>
                                                    <div class="card-body">
                                                        <p><strong>العنوان:</strong> <?php echo $conflict['lecture1']['title']; ?></p>
                                                        <p><strong>المادة:</strong> <?php echo $conflict['lecture1']['subject']; ?></p>
                                                        <?php if ($conflict['type'] == 'قاعة'): ?>
                                                            <p><strong>الأستاذ:</strong> <?php echo $conflict['lecture1']['professor']; ?></p>
                                                        <?php else: ?>
                                                            <p><strong>القاعة:</strong> <?php echo $conflict['lecture1']['hall']; ?></p>
                                                        <?php endif; ?>
                                                        <p><strong>اليوم:</strong> <?php echo $conflict['day']; ?></p>
                                                        <p><strong>الوقت:</strong> <?php echo $conflict['lecture1']['time']; ?></p>
                                                        <a href="edit_lecture.php?id=<?php echo $conflict['lecture1']['id']; ?>" class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit me-1"></i>تعديل
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <div class="card border-warning">
                                                    <div class="card-header bg-warning text-dark">
                                                        <strong>المحاضرة الثانية</strong>
                                                    </div>
                                                    <div class="card-body">
                                                        <p><strong>العنوان:</strong> <?php echo $conflict['lecture2']['title']; ?></p>
                                                        <p><strong>المادة:</strong> <?php echo $conflict['lecture2']['subject']; ?></p>
                                                        <?php if ($conflict['type'] == 'قاعة'): ?>
                                                            <p><strong>الأستاذ:</strong> <?php echo $conflict['lecture2']['professor']; ?></p>
                                                        <?php else: ?>
                                                            <p><strong>القاعة:</strong> <?php echo $conflict['lecture2']['hall']; ?></p>
                                                        <?php endif; ?>
                                                        <p><strong>اليوم:</strong> <?php echo $conflict['day']; ?></p>
                                                        <p><strong>الوقت:</strong> <?php echo $conflict['lecture2']['time']; ?></p>
                                                        <a href="edit_lecture.php?id=<?php echo $conflict['lecture2']['id']; ?>" class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit me-1"></i>تعديل
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- نصائح لحل التعارضات -->
        <?php if (!empty($conflicts)): ?>
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>
                                نصائح لحل التعارضات
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="mb-0">
                                <li><strong>تغيير الوقت:</strong> قم بتعديل وقت إحدى المحاضرات المتعارضة</li>
                                <li><strong>تغيير القاعة:</strong> انقل إحدى المحاضرات إلى قاعة أخرى متاحة</li>
                                <li><strong>تغيير اليوم:</strong> انقل إحدى المحاضرات إلى يوم آخر</li>
                                <li><strong>إلغاء المحاضرة:</strong> قم بإلغاء إحدى المحاضرات إذا لم تعد مطلوبة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- أزرار الإجراءات -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="index.php" class="btn btn-primary me-2">
                    <i class="fas fa-home me-2"></i>العودة للرئيسية
                </a>
                <a href="lectures.php" class="btn btn-secondary me-2">
                    <i class="fas fa-list me-2"></i>إدارة المحاضرات
                </a>
                <button onclick="window.location.reload()" class="btn btn-info">
                    <i class="fas fa-sync-alt me-2"></i>إعادة فحص
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
