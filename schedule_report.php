<?php
require_once 'config.php';

// جلب المحاضرات مع التفاصيل
$query = "SELECT l.*, s.name as subject_name, s.code as subject_code, 
                 p.name as professor_name, h.name as hall_name, h.location as hall_location
          FROM lectures l
          JOIN subjects s ON l.subject_id = s.id
          JOIN professors p ON l.professor_id = p.id
          JOIN halls h ON l.hall_id = h.id
          WHERE l.status = 'مجدولة'
          ORDER BY 
            CASE l.day_of_week 
                WHEN 'الأحد' THEN 1
                WHEN 'الاثنين' THEN 2
                WHEN 'الثلاثاء' THEN 3
                WHEN 'الأربعاء' THEN 4
                WHEN 'الخميس' THEN 5
                WHEN 'الجمعة' THEN 6
                WHEN 'السبت' THEN 7
            END, l.start_time ASC";

$stmt = $db->prepare($query);
$stmt->execute();
$lectures = $stmt->fetchAll();

// تجميع المحاضرات حسب اليوم
$schedule = [];
$days_order = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

foreach ($days_order as $day) {
    $schedule[$day] = [];
}

foreach ($lectures as $lecture) {
    $schedule[$lecture['day_of_week']][] = $lecture;
}

// إحصائيات
$total_lectures = count($lectures);
$total_halls = $db->query("SELECT COUNT(*) FROM halls WHERE status = 'متاحة'")->fetchColumn();
$total_professors = $db->query("SELECT COUNT(*) FROM professors")->fetchColumn();
$total_subjects = $db->query("SELECT COUNT(*) FROM subjects")->fetchColumn();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الجدولة - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .navbar { display: none !important; }
            body { font-size: 12px; }
            .card { border: 1px solid #000 !important; }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calendar-alt me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="lectures.php">
                            <i class="fas fa-chalkboard-teacher me-1"></i>المحاضرات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="halls.php">
                            <i class="fas fa-building me-1"></i>القاعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="professors.php">
                            <i class="fas fa-user-tie me-1"></i>الأساتذة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-1"></i>المواد
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان التقرير -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h1>
                    <i class="fas fa-file-alt text-primary me-2"></i>
                    تقرير جدولة المحاضرات
                </h1>
                <p class="text-muted">تاريخ التقرير: <?php echo date('Y-m-d H:i'); ?></p>
            </div>
        </div>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-chalkboard-teacher fa-2x text-primary mb-2"></i>
                        <h4><?php echo $total_lectures; ?></h4>
                        <p class="mb-0">محاضرة مجدولة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-building fa-2x text-success mb-2"></i>
                        <h4><?php echo $total_halls; ?></h4>
                        <p class="mb-0">قاعة متاحة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-user-tie fa-2x text-info mb-2"></i>
                        <h4><?php echo $total_professors; ?></h4>
                        <p class="mb-0">أستاذ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-book fa-2x text-warning mb-2"></i>
                        <h4><?php echo $total_subjects; ?></h4>
                        <p class="mb-0">مادة دراسية</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="row mb-3 no-print">
            <div class="col-12 text-center">
                <button onclick="window.print()" class="btn btn-success me-2">
                    <i class="fas fa-print me-2"></i>طباعة التقرير
                </button>
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>العودة للرئيسية
                </a>
            </div>
        </div>

        <!-- الجدولة الأسبوعية -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-week me-2"></i>
                            الجدولة الأسبوعية التفصيلية
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($days_order as $day): ?>
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-calendar-day me-2"></i>
                                <?php echo $day; ?>
                                <span class="badge bg-secondary ms-2"><?php echo count($schedule[$day]); ?> محاضرة</span>
                            </h6>
                            
                            <?php if (empty($schedule[$day])): ?>
                                <div class="alert alert-light text-center mb-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    لا توجد محاضرات مجدولة في هذا اليوم
                                </div>
                            <?php else: ?>
                                <div class="table-responsive mb-4">
                                    <table class="table table-sm table-striped">
                                        <thead class="table-light">
                                            <tr>
                                                <th>الوقت</th>
                                                <th>المادة</th>
                                                <th>الأستاذ</th>
                                                <th>القاعة</th>
                                                <th>الموقع</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($schedule[$day] as $lecture): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo format_time($lecture['start_time']); ?></strong>
                                                        -
                                                        <?php echo format_time($lecture['end_time']); ?>
                                                    </td>
                                                    <td>
                                                        <strong><?php echo $lecture['subject_name']; ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo $lecture['subject_code']; ?></small>
                                                    </td>
                                                    <td><?php echo $lecture['professor_name']; ?></td>
                                                    <td>
                                                        <span class="badge bg-primary"><?php echo $lecture['hall_name']; ?></span>
                                                    </td>
                                                    <td>
                                                        <small class="text-muted"><?php echo $lecture['hall_location']; ?></small>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
