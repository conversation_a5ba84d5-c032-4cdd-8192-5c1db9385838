<?php
require_once 'config.php';

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $subject_id = (int)$_POST['subject_id'];
                $professor_id = (int)$_POST['professor_id'];
                $hall_id = (int)$_POST['hall_id'];
                $title = sanitize_input($_POST['title']);
                $lecture_date = $_POST['lecture_date'];
                $start_time = $_POST['start_time'];
                $end_time = $_POST['end_time'];
                $day_of_week = sanitize_input($_POST['day_of_week']);
                $semester = sanitize_input($_POST['semester']);
                $academic_year = sanitize_input($_POST['academic_year']);
                $notes = sanitize_input($_POST['notes']);
                
                // التحقق من عدم وجود تعارض في الوقت
                $conflict_query = "SELECT COUNT(*) FROM lectures 
                                  WHERE (hall_id = ? OR professor_id = ?) 
                                  AND day_of_week = ? 
                                  AND status = 'مجدولة'
                                  AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))";
                $conflict_stmt = $db->prepare($conflict_query);
                $conflict_stmt->execute([$hall_id, $professor_id, $day_of_week, $start_time, $start_time, $end_time, $end_time]);
                $conflicts = $conflict_stmt->fetchColumn();
                
                if ($conflicts > 0) {
                    $message = "يوجد تعارض في الوقت! القاعة أو الأستاذ مشغول في هذا الوقت";
                    $message_type = 'error';
                } else {
                    $query = "INSERT INTO lectures (subject_id, professor_id, hall_id, title, lecture_date, start_time, end_time, day_of_week, semester, academic_year, notes) 
                             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $stmt = $db->prepare($query);
                    
                    if ($stmt->execute([$subject_id, $professor_id, $hall_id, $title, $lecture_date, $start_time, $end_time, $day_of_week, $semester, $academic_year, $notes])) {
                        $message = "تم إضافة المحاضرة بنجاح";
                        $message_type = 'success';
                    } else {
                        $message = "حدث خطأ في إضافة المحاضرة";
                        $message_type = 'error';
                    }
                }
                break;
                
            case 'edit':
                $id = (int)$_POST['id'];
                $subject_id = (int)$_POST['subject_id'];
                $professor_id = (int)$_POST['professor_id'];
                $hall_id = (int)$_POST['hall_id'];
                $title = sanitize_input($_POST['title']);
                $lecture_date = $_POST['lecture_date'];
                $start_time = $_POST['start_time'];
                $end_time = $_POST['end_time'];
                $day_of_week = sanitize_input($_POST['day_of_week']);
                $semester = sanitize_input($_POST['semester']);
                $academic_year = sanitize_input($_POST['academic_year']);
                $notes = sanitize_input($_POST['notes']);
                $status = sanitize_input($_POST['status']);
                
                // التحقق من عدم وجود تعارض في الوقت (باستثناء المحاضرة الحالية)
                $conflict_query = "SELECT COUNT(*) FROM lectures 
                                  WHERE (hall_id = ? OR professor_id = ?) 
                                  AND day_of_week = ? 
                                  AND status = 'مجدولة'
                                  AND id != ?
                                  AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))";
                $conflict_stmt = $db->prepare($conflict_query);
                $conflict_stmt->execute([$hall_id, $professor_id, $day_of_week, $id, $start_time, $start_time, $end_time, $end_time]);
                $conflicts = $conflict_stmt->fetchColumn();
                
                if ($conflicts > 0) {
                    $message = "يوجد تعارض في الوقت! القاعة أو الأستاذ مشغول في هذا الوقت";
                    $message_type = 'error';
                } else {
                    $query = "UPDATE lectures SET subject_id = ?, professor_id = ?, hall_id = ?, title = ?, lecture_date = ?, 
                             start_time = ?, end_time = ?, day_of_week = ?, semester = ?, academic_year = ?, notes = ?, status = ? 
                             WHERE id = ?";
                    $stmt = $db->prepare($query);
                    
                    if ($stmt->execute([$subject_id, $professor_id, $hall_id, $title, $lecture_date, $start_time, $end_time, $day_of_week, $semester, $academic_year, $notes, $status, $id])) {
                        $message = "تم تحديث المحاضرة بنجاح";
                        $message_type = 'success';
                    } else {
                        $message = "حدث خطأ في تحديث المحاضرة";
                        $message_type = 'error';
                    }
                }
                break;
                
            case 'delete':
                $id = (int)$_POST['id'];
                
                $query = "DELETE FROM lectures WHERE id = ?";
                $stmt = $db->prepare($query);
                
                if ($stmt->execute([$id])) {
                    $message = "تم حذف المحاضرة بنجاح";
                    $message_type = 'success';
                } else {
                    $message = "حدث خطأ في حذف المحاضرة";
                    $message_type = 'error';
                }
                break;
        }
    }
}

// جلب جميع المحاضرات مع التفاصيل
$query = "SELECT l.*, s.name as subject_name, s.code as subject_code, 
                 p.name as professor_name, h.name as hall_name
          FROM lectures l
          JOIN subjects s ON l.subject_id = s.id
          JOIN professors p ON l.professor_id = p.id
          JOIN halls h ON l.hall_id = h.id
          ORDER BY l.lecture_date DESC, l.start_time ASC";
$stmt = $db->prepare($query);
$stmt->execute();
$lectures = $stmt->fetchAll();

// جلب البيانات المساعدة للنماذج
$subjects_query = "SELECT * FROM subjects ORDER BY name ASC";
$subjects_stmt = $db->prepare($subjects_query);
$subjects_stmt->execute();
$subjects = $subjects_stmt->fetchAll();

$professors_query = "SELECT * FROM professors ORDER BY name ASC";
$professors_stmt = $db->prepare($professors_query);
$professors_stmt->execute();
$professors = $professors_stmt->fetchAll();

$halls_query = "SELECT * FROM halls WHERE status = 'متاحة' ORDER BY name ASC";
$halls_stmt = $db->prepare($halls_query);
$halls_stmt->execute();
$available_halls = $halls_stmt->fetchAll();

// جلب بيانات المحاضرة للتعديل
$edit_lecture = null;
if (isset($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    $query = "SELECT * FROM lectures WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$edit_id]);
    $edit_lecture = $stmt->fetch();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المحاضرات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calendar-alt me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="lectures.php">
                            <i class="fas fa-chalkboard-teacher me-1"></i>المحاضرات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="halls.php">
                            <i class="fas fa-building me-1"></i>القاعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="professors.php">
                            <i class="fas fa-user-tie me-1"></i>الأساتذة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-1"></i>المواد
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <?php echo show_message($message, $message_type); ?>
        <?php endif; ?>

        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center">
                    <i class="fas fa-chalkboard-teacher text-primary me-2"></i>
                    إدارة المحاضرات
                </h1>
            </div>
        </div>

        <!-- زر إضافة محاضرة جديدة -->
        <div class="row mb-3">
            <div class="col-12 text-center">
                <a href="add_lecture.php" class="btn btn-success btn-lg">
                    <i class="fas fa-plus me-2"></i>إضافة محاضرة جديدة
                </a>
            </div>
        </div>

        <!-- قائمة المحاضرات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة المحاضرات (<?php echo count($lectures); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($lectures)): ?>
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد محاضرات مسجلة
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>المادة</th>
                                            <th>الأستاذ</th>
                                            <th>القاعة</th>
                                            <th>اليوم</th>
                                            <th>الوقت</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($lectures as $lecture): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo $lecture['subject_name']; ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo $lecture['subject_code']; ?></small>
                                                </td>
                                                <td><?php echo $lecture['professor_name']; ?></td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo $lecture['hall_name']; ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo $lecture['day_of_week']; ?></span>
                                                </td>
                                                <td>
                                                    <strong><?php echo format_time($lecture['start_time']); ?></strong>
                                                    -
                                                    <?php echo format_time($lecture['end_time']); ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch ($lecture['status']) {
                                                        case 'مجدولة':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'ملغية':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                        case 'مؤجلة':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'منتهية':
                                                            $status_class = 'bg-secondary';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>"><?php echo $lecture['status']; ?></span>
                                                </td>
                                                <td>
                                                    <a href="edit_lecture.php?id=<?php echo $lecture['id']; ?>" class="btn btn-sm btn-warning me-1">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger me-1" 
                                                            onclick="confirmDelete(<?php echo $lecture['id']; ?>, '<?php echo $lecture['title']; ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-info" 
                                                            onclick="showDetails(<?php echo $lecture['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج حذف المحاضرة -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف المحاضرة <strong id="lectureNameToDelete"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" id="lectureIdToDelete">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج عرض تفاصيل المحاضرة -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">تفاصيل المحاضرة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="lectureDetails">
                    <!-- سيتم ملء التفاصيل هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(id, title) {
            document.getElementById('lectureIdToDelete').value = id;
            document.getElementById('lectureNameToDelete').textContent = title;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function showDetails(id) {
            // جلب تفاصيل المحاضرة
            fetch('get_lecture_details.php?id=' + id)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('lectureDetails').innerHTML = data;
                    new bootstrap.Modal(document.getElementById('detailsModal')).show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في جلب التفاصيل');
                });
        }
    </script>
</body>
</html>
