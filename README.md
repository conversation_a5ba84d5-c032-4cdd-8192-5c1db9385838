# نظام جدولة القاعات والمحاضرات

نظام شامل لإدارة وجدولة المحاضرات والقاعات الدراسية مطور بلغة PHP بدون استخدام أطر العمل.

## المميزات

- **إدارة القاعات**: إضافة وتعديل وحذف القاعات مع تتبع السعة والمعدات
- **إدارة الأساتذة**: إدارة بيانات أعضاء هيئة التدريس
- **إدارة المواد**: إدارة المواد الدراسية والساعات المعتمدة
- **جدولة المحاضرات**: إنشاء وتعديل جداول المحاضرات
- **فحص التعارضات**: اكتشاف تلقائي للتعارضات في الأوقات والقاعات
- **تقارير شاملة**: عرض تقارير مفصلة للجدولة
- **واجهة عربية**: دعم كامل للغة العربية مع تصميم متجاوب

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)

## التثبيت

### 1. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE lecture_hall_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. استيراد الجداول

قم بتشغيل ملف `database.sql` في قاعدة البيانات:

```bash
mysql -u root -p lecture_hall_system < database.sql
```

### 3. تكوين الاتصال

قم بتعديل ملف `config.php` وضبط إعدادات قاعدة البيانات:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'lecture_hall_system');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 4. رفع الملفات

قم برفع جميع الملفات إلى مجلد الخادم (مثل htdocs في XAMPP)

### 5. الوصول للنظام

افتح المتصفح وانتقل إلى: `http://localhost/uni`

## هيكل الملفات

```
uni/
├── config.php              # إعدادات قاعدة البيانات والدوال المساعدة
├── database.sql            # هيكل قاعدة البيانات والبيانات التجريبية
├── index.php               # الصفحة الرئيسية
├── style.css               # ملف التصميم
├── halls.php               # إدارة القاعات
├── professors.php          # إدارة الأساتذة
├── subjects.php            # إدارة المواد
├── lectures.php            # إدارة المحاضرات
├── add_lecture.php         # إضافة محاضرة جديدة
├── edit_lecture.php        # تعديل المحاضرة
├── conflicts.php           # فحص التعارضات
├── schedule_report.php     # تقرير الجدولة
├── get_hall_details.php    # تفاصيل القاعة
├── get_professor_details.php # تفاصيل الأستاذ
├── get_subject_details.php # تفاصيل المادة
├── get_lecture_details.php # تفاصيل المحاضرة
└── README.md               # دليل الاستخدام
```

## كيفية الاستخدام

### 1. إدارة القاعات
- انتقل إلى صفحة "القاعات"
- أضف قاعات جديدة مع تحديد السعة والموقع والمعدات
- عدّل أو احذف القاعات الموجودة
- اعرض تفاصيل كل قاعة والمحاضرات المجدولة بها

### 2. إدارة الأساتذة
- انتقل إلى صفحة "الأساتذة"
- أضف بيانات أعضاء هيئة التدريس
- عدّل معلومات الأساتذة
- اعرض جدولة كل أستاذ

### 3. إدارة المواد
- انتقل إلى صفحة "المواد"
- أضف المواد الدراسية مع رموزها والساعات المعتمدة
- عدّل معلومات المواد
- اعرض المحاضرات المرتبطة بكل مادة

### 4. جدولة المحاضرات
- انتقل إلى صفحة "المحاضرات"
- أضف محاضرات جديدة مع تحديد المادة والأستاذ والقاعة والوقت
- النظام يتحقق تلقائياً من عدم وجود تعارضات
- عدّل أو احذف المحاضرات الموجودة

### 5. فحص التعارضات
- انتقل إلى صفحة "فحص التعارضات"
- اعرض جميع التعارضات في الجدولة
- احصل على اقتراحات لحل التعارضات

### 6. التقارير
- انتقل إلى صفحة "تقرير الجدولة"
- اعرض الجدولة الأسبوعية الكاملة
- اطبع التقرير أو احفظه

## الأمان

- جميع المدخلات يتم تنظيفها وحمايتها من هجمات SQL Injection
- استخدام Prepared Statements في جميع استعلامات قاعدة البيانات
- تشفير البيانات الحساسة

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا النظام مطور للاستخدام التعليمي والأكاديمي.
