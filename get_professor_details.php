<?php
require_once 'config.php';

if (isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    
    // جلب تفاصيل الأستاذ
    $query = "SELECT * FROM professors WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$id]);
    $professor = $stmt->fetch();
    
    if ($professor) {
        // جلب المحاضرات المرتبطة بالأستاذ
        $lectures_query = "SELECT l.*, s.name as subject_name, h.name as hall_name
                          FROM lectures l
                          JOIN subjects s ON l.subject_id = s.id
                          JOIN halls h ON l.hall_id = h.id
                          WHERE l.professor_id = ? AND l.status = 'مجدولة'
                          ORDER BY l.day_of_week, l.start_time";
        $lectures_stmt = $db->prepare($lectures_query);
        $lectures_stmt->execute([$id]);
        $lectures = $lectures_stmt->fetchAll();
        
        // جلب المواد التي يدرسها الأستاذ
        $subjects_query = "SELECT DISTINCT s.name, s.code
                          FROM subjects s
                          JOIN lectures l ON s.id = l.subject_id
                          WHERE l.professor_id = ?";
        $subjects_stmt = $db->prepare($subjects_query);
        $subjects_stmt->execute([$id]);
        $subjects = $subjects_stmt->fetchAll();
        
        echo '<div class="row">';
        echo '<div class="col-md-6">';
        echo '<h6 class="text-primary">معلومات الأستاذ</h6>';
        echo '<table class="table table-sm">';
        echo '<tr><td><strong>الاسم:</strong></td><td>' . $professor['name'] . '</td></tr>';
        echo '<tr><td><strong>البريد الإلكتروني:</strong></td><td>' . ($professor['email'] ?: '-') . '</td></tr>';
        echo '<tr><td><strong>رقم الهاتف:</strong></td><td>' . ($professor['phone'] ?: '-') . '</td></tr>';
        echo '<tr><td><strong>القسم:</strong></td><td>' . ($professor['department'] ?: '-') . '</td></tr>';
        echo '<tr><td><strong>التخصص:</strong></td><td>' . ($professor['specialization'] ?: '-') . '</td></tr>';
        echo '<tr><td><strong>تاريخ التسجيل:</strong></td><td>' . date('Y-m-d H:i', strtotime($professor['created_at'])) . '</td></tr>';
        echo '</table>';
        
        if (!empty($subjects)) {
            echo '<h6 class="text-primary mt-3">المواد التي يدرسها (' . count($subjects) . ')</h6>';
            echo '<ul class="list-group list-group-flush">';
            foreach ($subjects as $subject) {
                echo '<li class="list-group-item d-flex justify-content-between align-items-center">';
                echo $subject['name'];
                echo '<span class="badge bg-primary rounded-pill">' . $subject['code'] . '</span>';
                echo '</li>';
            }
            echo '</ul>';
        }
        
        echo '</div>';
        echo '<div class="col-md-6">';
        echo '<h6 class="text-primary">الجدولة الأسبوعية (' . count($lectures) . ')</h6>';
        
        if (empty($lectures)) {
            echo '<div class="alert alert-info">لا توجد محاضرات مجدولة لهذا الأستاذ</div>';
        } else {
            echo '<div class="table-responsive">';
            echo '<table class="table table-sm table-striped">';
            echo '<thead><tr><th>اليوم</th><th>الوقت</th><th>المادة</th><th>القاعة</th></tr></thead>';
            echo '<tbody>';
            
            foreach ($lectures as $lecture) {
                echo '<tr>';
                echo '<td><span class="badge bg-secondary">' . $lecture['day_of_week'] . '</span></td>';
                echo '<td>' . format_time($lecture['start_time']) . ' - ' . format_time($lecture['end_time']) . '</td>';
                echo '<td>' . $lecture['subject_name'] . '</td>';
                echo '<td>' . $lecture['hall_name'] . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
            echo '</div>';
        }
        
        echo '</div>';
        echo '</div>';
        
    } else {
        echo '<div class="alert alert-danger">الأستاذ غير موجود</div>';
    }
} else {
    echo '<div class="alert alert-danger">معرف الأستاذ مطلوب</div>';
}
?>
