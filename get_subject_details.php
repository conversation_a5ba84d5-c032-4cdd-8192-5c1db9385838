<?php
require_once 'config.php';

if (isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    
    // جلب تفاصيل المادة
    $query = "SELECT * FROM subjects WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$id]);
    $subject = $stmt->fetch();
    
    if ($subject) {
        // جلب المحاضرات المرتبطة بالمادة
        $lectures_query = "SELECT l.*, p.name as professor_name, h.name as hall_name
                          FROM lectures l
                          JOIN professors p ON l.professor_id = p.id
                          JOIN halls h ON l.hall_id = h.id
                          WHERE l.subject_id = ? AND l.status = 'مجدولة'
                          ORDER BY l.day_of_week, l.start_time";
        $lectures_stmt = $db->prepare($lectures_query);
        $lectures_stmt->execute([$id]);
        $lectures = $lectures_stmt->fetchAll();
        
        // جلب الأساتذة الذين يدرسون هذه المادة
        $professors_query = "SELECT DISTINCT p.name, p.email, p.department
                            FROM professors p
                            JOIN lectures l ON p.id = l.professor_id
                            WHERE l.subject_id = ?";
        $professors_stmt = $db->prepare($professors_query);
        $professors_stmt->execute([$id]);
        $professors = $professors_stmt->fetchAll();
        
        echo '<div class="row">';
        echo '<div class="col-md-6">';
        echo '<h6 class="text-primary">معلومات المادة</h6>';
        echo '<table class="table table-sm">';
        echo '<tr><td><strong>اسم المادة:</strong></td><td>' . $subject['name'] . '</td></tr>';
        echo '<tr><td><strong>رمز المادة:</strong></td><td><span class="badge bg-primary">' . $subject['code'] . '</span></td></tr>';
        echo '<tr><td><strong>الساعات المعتمدة:</strong></td><td>' . $subject['credit_hours'] . ' ساعة</td></tr>';
        echo '<tr><td><strong>القسم:</strong></td><td>' . ($subject['department'] ?: '-') . '</td></tr>';
        echo '<tr><td><strong>تاريخ الإنشاء:</strong></td><td>' . date('Y-m-d H:i', strtotime($subject['created_at'])) . '</td></tr>';
        echo '</table>';
        
        if (!empty($subject['description'])) {
            echo '<h6 class="text-primary mt-3">وصف المادة</h6>';
            echo '<p class="text-muted">' . nl2br($subject['description']) . '</p>';
        }
        
        if (!empty($professors)) {
            echo '<h6 class="text-primary mt-3">الأساتذة المدرسون (' . count($professors) . ')</h6>';
            echo '<ul class="list-group list-group-flush">';
            foreach ($professors as $professor) {
                echo '<li class="list-group-item d-flex justify-content-between align-items-center">';
                echo $professor['name'];
                if (!empty($professor['department'])) {
                    echo '<span class="badge bg-secondary rounded-pill">' . $professor['department'] . '</span>';
                }
                echo '</li>';
            }
            echo '</ul>';
        }
        
        echo '</div>';
        echo '<div class="col-md-6">';
        echo '<h6 class="text-primary">الجدولة الأسبوعية (' . count($lectures) . ')</h6>';
        
        if (empty($lectures)) {
            echo '<div class="alert alert-info">لا توجد محاضرات مجدولة لهذه المادة</div>';
        } else {
            echo '<div class="table-responsive">';
            echo '<table class="table table-sm table-striped">';
            echo '<thead><tr><th>اليوم</th><th>الوقت</th><th>الأستاذ</th><th>القاعة</th></tr></thead>';
            echo '<tbody>';
            
            foreach ($lectures as $lecture) {
                echo '<tr>';
                echo '<td><span class="badge bg-secondary">' . $lecture['day_of_week'] . '</span></td>';
                echo '<td>' . format_time($lecture['start_time']) . ' - ' . format_time($lecture['end_time']) . '</td>';
                echo '<td>' . $lecture['professor_name'] . '</td>';
                echo '<td>' . $lecture['hall_name'] . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
            echo '</div>';
        }
        
        echo '</div>';
        echo '</div>';
        
    } else {
        echo '<div class="alert alert-danger">المادة غير موجودة</div>';
    }
} else {
    echo '<div class="alert alert-danger">معرف المادة مطلوب</div>';
}
?>
