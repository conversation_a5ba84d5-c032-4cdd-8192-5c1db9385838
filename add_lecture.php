<?php
require_once 'config.php';

// معالجة إضافة المحاضرة
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $subject_id = (int)$_POST['subject_id'];
    $professor_id = (int)$_POST['professor_id'];
    $hall_id = (int)$_POST['hall_id'];
    $title = sanitize_input($_POST['title']);
    $lecture_date = $_POST['lecture_date'];
    $start_time = $_POST['start_time'];
    $end_time = $_POST['end_time'];
    $day_of_week = sanitize_input($_POST['day_of_week']);
    $semester = sanitize_input($_POST['semester']);
    $academic_year = sanitize_input($_POST['academic_year']);
    $notes = sanitize_input($_POST['notes']);
    
    // التحقق من صحة البيانات
    if (strtotime($end_time) <= strtotime($start_time)) {
        $message = "وقت انتهاء المحاضرة يجب أن يكون بعد وقت البداية";
        $message_type = 'error';
    } else {
        // التحقق من عدم وجود تعارض في الوقت
        $conflict_query = "SELECT COUNT(*) FROM lectures 
                          WHERE (hall_id = ? OR professor_id = ?) 
                          AND day_of_week = ? 
                          AND status = 'مجدولة'
                          AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))";
        $conflict_stmt = $db->prepare($conflict_query);
        $conflict_stmt->execute([$hall_id, $professor_id, $day_of_week, $start_time, $start_time, $end_time, $end_time]);
        $conflicts = $conflict_stmt->fetchColumn();
        
        if ($conflicts > 0) {
            $message = "يوجد تعارض في الوقت! القاعة أو الأستاذ مشغول في هذا الوقت";
            $message_type = 'error';
        } else {
            $query = "INSERT INTO lectures (subject_id, professor_id, hall_id, title, lecture_date, start_time, end_time, day_of_week, semester, academic_year, notes) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($query);
            
            if ($stmt->execute([$subject_id, $professor_id, $hall_id, $title, $lecture_date, $start_time, $end_time, $day_of_week, $semester, $academic_year, $notes])) {
                $message = "تم إضافة المحاضرة بنجاح";
                $message_type = 'success';
                // إعادة توجيه إلى صفحة المحاضرات
                header("Location: lectures.php");
                exit();
            } else {
                $message = "حدث خطأ في إضافة المحاضرة";
                $message_type = 'error';
            }
        }
    }
}

// جلب البيانات المساعدة
$subjects_query = "SELECT * FROM subjects ORDER BY name ASC";
$subjects_stmt = $db->prepare($subjects_query);
$subjects_stmt->execute();
$subjects = $subjects_stmt->fetchAll();

$professors_query = "SELECT * FROM professors ORDER BY name ASC";
$professors_stmt = $db->prepare($professors_query);
$professors_stmt->execute();
$professors = $professors_stmt->fetchAll();

$halls_query = "SELECT * FROM halls WHERE status = 'متاحة' ORDER BY name ASC";
$halls_stmt = $db->prepare($halls_query);
$halls_stmt->execute();
$available_halls = $halls_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة محاضرة جديدة - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calendar-alt me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="lectures.php">
                            <i class="fas fa-chalkboard-teacher me-1"></i>المحاضرات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="halls.php">
                            <i class="fas fa-building me-1"></i>القاعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="professors.php">
                            <i class="fas fa-user-tie me-1"></i>الأساتذة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-1"></i>المواد
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <?php echo show_message($message, $message_type); ?>
        <?php endif; ?>

        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center">
                    <i class="fas fa-plus text-success me-2"></i>
                    إضافة محاضرة جديدة
                </h1>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chalkboard-teacher me-2"></i>
                            بيانات المحاضرة
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="subject_id" class="form-label">المادة الدراسية</label>
                                        <select class="form-select" id="subject_id" name="subject_id" required>
                                            <option value="">اختر المادة</option>
                                            <?php foreach ($subjects as $subject): ?>
                                                <option value="<?php echo $subject['id']; ?>">
                                                    <?php echo $subject['name'] . ' (' . $subject['code'] . ')'; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="professor_id" class="form-label">الأستاذ</label>
                                        <select class="form-select" id="professor_id" name="professor_id" required>
                                            <option value="">اختر الأستاذ</option>
                                            <?php foreach ($professors as $professor): ?>
                                                <option value="<?php echo $professor['id']; ?>">
                                                    <?php echo $professor['name']; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="hall_id" class="form-label">القاعة</label>
                                        <select class="form-select" id="hall_id" name="hall_id" required>
                                            <option value="">اختر القاعة</option>
                                            <?php foreach ($available_halls as $hall): ?>
                                                <option value="<?php echo $hall['id']; ?>">
                                                    <?php echo $hall['name'] . ' (سعة: ' . $hall['capacity'] . ')'; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">عنوان المحاضرة</label>
                                        <input type="text" class="form-control" id="title" name="title" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="day_of_week" class="form-label">يوم الأسبوع</label>
                                        <select class="form-select" id="day_of_week" name="day_of_week" required>
                                            <option value="">اختر اليوم</option>
                                            <option value="الأحد">الأحد</option>
                                            <option value="الاثنين">الاثنين</option>
                                            <option value="الثلاثاء">الثلاثاء</option>
                                            <option value="الأربعاء">الأربعاء</option>
                                            <option value="الخميس">الخميس</option>
                                            <option value="الجمعة">الجمعة</option>
                                            <option value="السبت">السبت</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="start_time" class="form-label">وقت البداية</label>
                                        <input type="time" class="form-control" id="start_time" name="start_time" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="end_time" class="form-label">وقت النهاية</label>
                                        <input type="time" class="form-control" id="end_time" name="end_time" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="lecture_date" class="form-label">تاريخ المحاضرة</label>
                                        <input type="date" class="form-control" id="lecture_date" name="lecture_date" 
                                               value="<?php echo date('Y-m-d'); ?>" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="semester" class="form-label">الفصل الدراسي</label>
                                        <select class="form-select" id="semester" name="semester" required>
                                            <option value="">اختر الفصل</option>
                                            <option value="الفصل الأول">الفصل الأول</option>
                                            <option value="الفصل الثاني">الفصل الثاني</option>
                                            <option value="الفصل الصيفي">الفصل الصيفي</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="academic_year" class="form-label">السنة الأكاديمية</label>
                                        <input type="text" class="form-control" id="academic_year" name="academic_year" 
                                               value="2023-2024" placeholder="مثال: 2023-2024" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="أي ملاحظات إضافية حول المحاضرة..."></textarea>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="lectures.php" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-arrow-right me-2"></i>العودة
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>إضافة المحاضرة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- نصائح مفيدة -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            نصائح مفيدة
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li>تأكد من أن وقت انتهاء المحاضرة بعد وقت البداية</li>
                            <li>النظام سيتحقق تلقائياً من عدم وجود تعارض في الأوقات</li>
                            <li>يمكن إضافة ملاحظات خاصة بالمحاضرة في حقل الملاحظات</li>
                            <li>تأكد من اختيار القاعة المناسبة لعدد الطلاب المتوقع</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث يوم الأسبوع تلقائياً عند تغيير التاريخ
        document.getElementById('lecture_date').addEventListener('change', function() {
            const date = new Date(this.value);
            const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
            const dayName = days[date.getDay()];
            
            const daySelect = document.getElementById('day_of_week');
            for (let option of daySelect.options) {
                if (option.value === dayName) {
                    option.selected = true;
                    break;
                }
            }
        });
        
        // التحقق من صحة الأوقات
        document.getElementById('end_time').addEventListener('change', function() {
            const startTime = document.getElementById('start_time').value;
            const endTime = this.value;
            
            if (startTime && endTime && endTime <= startTime) {
                alert('وقت انتهاء المحاضرة يجب أن يكون بعد وقت البداية');
                this.value = '';
            }
        });
    </script>
</body>
</html>
