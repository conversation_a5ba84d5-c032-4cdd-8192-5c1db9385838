<?php
require_once 'config.php';

// استعلام لجلب المحاضرات مع تفاصيل المواد والأساتذة والقاعات
$query = "SELECT l.*, s.name as subject_name, s.code as subject_code, 
                 p.name as professor_name, h.name as hall_name, h.location as hall_location
          FROM lectures l
          JOIN subjects s ON l.subject_id = s.id
          JOIN professors p ON l.professor_id = p.id
          JOIN halls h ON l.hall_id = h.id
          WHERE l.status = 'مجدولة'
          ORDER BY l.lecture_date ASC, l.start_time ASC";

$stmt = $db->prepare($query);
$stmt->execute();
$lectures = $stmt->fetchAll();

// تجميع المحاضرات حسب اليوم
$lectures_by_day = [];
foreach ($lectures as $lecture) {
    $day = $lecture['day_of_week'];
    if (!isset($lectures_by_day[$day])) {
        $lectures_by_day[$day] = [];
    }
    $lectures_by_day[$day][] = $lecture;
}

$days_order = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calendar-alt me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="lectures.php">
                            <i class="fas fa-chalkboard-teacher me-1"></i>المحاضرات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="halls.php">
                            <i class="fas fa-building me-1"></i>القاعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="professors.php">
                            <i class="fas fa-user-tie me-1"></i>الأساتذة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-1"></i>المواد
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center mb-3">
                    <i class="fas fa-calendar-week text-primary me-2"></i>
                    جدولة المحاضرات الأسبوعية
                </h1>
                <p class="text-center text-muted">عرض جميع المحاضرات المجدولة للأسبوع الحالي</p>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                        <h4><?php echo count($lectures); ?></h4>
                        <p class="mb-0">محاضرة مجدولة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-building fa-2x mb-2"></i>
                        <h4><?php 
                            $hall_count = $db->query("SELECT COUNT(*) FROM halls WHERE status = 'متاحة'")->fetchColumn();
                            echo $hall_count;
                        ?></h4>
                        <p class="mb-0">قاعة متاحة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-user-tie fa-2x mb-2"></i>
                        <h4><?php 
                            $prof_count = $db->query("SELECT COUNT(*) FROM professors")->fetchColumn();
                            echo $prof_count;
                        ?></h4>
                        <p class="mb-0">أستاذ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-book fa-2x mb-2"></i>
                        <h4><?php 
                            $subject_count = $db->query("SELECT COUNT(*) FROM subjects")->fetchColumn();
                            echo $subject_count;
                        ?></h4>
                        <p class="mb-0">مادة دراسية</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدولة المحاضرات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-week me-2"></i>
                            الجدولة الأسبوعية
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($lectures)): ?>
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد محاضرات مجدولة حالياً
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>اليوم</th>
                                            <th>الوقت</th>
                                            <th>المادة</th>
                                            <th>الأستاذ</th>
                                            <th>القاعة</th>
                                            <th>الموقع</th>
                                            <th>الملاحظات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($days_order as $day): ?>
                                            <?php if (isset($lectures_by_day[$day])): ?>
                                                <?php foreach ($lectures_by_day[$day] as $lecture): ?>
                                                    <tr>
                                                        <td>
                                                            <span class="badge bg-secondary"><?php echo $lecture['day_of_week']; ?></span>
                                                        </td>
                                                        <td>
                                                            <strong><?php echo format_time($lecture['start_time']); ?></strong>
                                                            -
                                                            <?php echo format_time($lecture['end_time']); ?>
                                                        </td>
                                                        <td>
                                                            <strong><?php echo $lecture['subject_name']; ?></strong>
                                                            <br>
                                                            <small class="text-muted"><?php echo $lecture['subject_code']; ?></small>
                                                        </td>
                                                        <td><?php echo $lecture['professor_name']; ?></td>
                                                        <td>
                                                            <span class="badge bg-primary"><?php echo $lecture['hall_name']; ?></span>
                                                        </td>
                                                        <td>
                                                            <small class="text-muted"><?php echo $lecture['hall_location']; ?></small>
                                                        </td>
                                                        <td>
                                                            <?php if (!empty($lecture['notes'])): ?>
                                                                <small><?php echo $lecture['notes']; ?></small>
                                                            <?php else: ?>
                                                                <span class="text-muted">-</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات السريعة -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="add_lecture.php" class="btn btn-success btn-lg me-2">
                    <i class="fas fa-plus me-2"></i>إضافة محاضرة جديدة
                </a>
                <a href="schedule_report.php" class="btn btn-info btn-lg me-2">
                    <i class="fas fa-file-pdf me-2"></i>تقرير الجدولة
                </a>
                <a href="conflicts.php" class="btn btn-warning btn-lg">
                    <i class="fas fa-exclamation-triangle me-2"></i>فحص التعارضات
                </a>
            </div>
        </div>
    </div>

    <!-- تذييل الصفحة -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">&copy; 2024 <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
