<?php
require_once 'config.php';

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = sanitize_input($_POST['name']);
                $email = sanitize_input($_POST['email']);
                $phone = sanitize_input($_POST['phone']);
                $department = sanitize_input($_POST['department']);
                $specialization = sanitize_input($_POST['specialization']);
                
                $query = "INSERT INTO professors (name, email, phone, department, specialization) VALUES (?, ?, ?, ?, ?)";
                $stmt = $db->prepare($query);
                
                if ($stmt->execute([$name, $email, $phone, $department, $specialization])) {
                    $message = "تم إضافة الأستاذ بنجاح";
                    $message_type = 'success';
                } else {
                    $message = "حدث خطأ في إضافة الأستاذ";
                    $message_type = 'error';
                }
                break;
                
            case 'edit':
                $id = (int)$_POST['id'];
                $name = sanitize_input($_POST['name']);
                $email = sanitize_input($_POST['email']);
                $phone = sanitize_input($_POST['phone']);
                $department = sanitize_input($_POST['department']);
                $specialization = sanitize_input($_POST['specialization']);
                
                $query = "UPDATE professors SET name = ?, email = ?, phone = ?, department = ?, specialization = ? WHERE id = ?";
                $stmt = $db->prepare($query);
                
                if ($stmt->execute([$name, $email, $phone, $department, $specialization, $id])) {
                    $message = "تم تحديث بيانات الأستاذ بنجاح";
                    $message_type = 'success';
                } else {
                    $message = "حدث خطأ في تحديث بيانات الأستاذ";
                    $message_type = 'error';
                }
                break;
                
            case 'delete':
                $id = (int)$_POST['id'];
                
                // التحقق من وجود محاضرات مرتبطة بالأستاذ
                $check_query = "SELECT COUNT(*) FROM lectures WHERE professor_id = ?";
                $check_stmt = $db->prepare($check_query);
                $check_stmt->execute([$id]);
                $lecture_count = $check_stmt->fetchColumn();
                
                if ($lecture_count > 0) {
                    $message = "لا يمكن حذف الأستاذ لأنه مرتبط بـ $lecture_count محاضرة";
                    $message_type = 'error';
                } else {
                    $query = "DELETE FROM professors WHERE id = ?";
                    $stmt = $db->prepare($query);
                    
                    if ($stmt->execute([$id])) {
                        $message = "تم حذف الأستاذ بنجاح";
                        $message_type = 'success';
                    } else {
                        $message = "حدث خطأ في حذف الأستاذ";
                        $message_type = 'error';
                    }
                }
                break;
        }
    }
}

// جلب جميع الأساتذة
$query = "SELECT * FROM professors ORDER BY name ASC";
$stmt = $db->prepare($query);
$stmt->execute();
$professors = $stmt->fetchAll();

// جلب بيانات الأستاذ للتعديل
$edit_professor = null;
if (isset($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    $query = "SELECT * FROM professors WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$edit_id]);
    $edit_professor = $stmt->fetch();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأساتذة - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calendar-alt me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="lectures.php">
                            <i class="fas fa-chalkboard-teacher me-1"></i>المحاضرات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="halls.php">
                            <i class="fas fa-building me-1"></i>القاعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="professors.php">
                            <i class="fas fa-user-tie me-1"></i>الأساتذة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-1"></i>المواد
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <?php echo show_message($message, $message_type); ?>
        <?php endif; ?>

        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center">
                    <i class="fas fa-user-tie text-primary me-2"></i>
                    إدارة الأساتذة
                </h1>
            </div>
        </div>

        <div class="row">
            <!-- نموذج إضافة/تعديل الأستاذ -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-plus me-2"></i>
                            <?php echo $edit_professor ? 'تعديل الأستاذ' : 'إضافة أستاذ جديد'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="<?php echo $edit_professor ? 'edit' : 'add'; ?>">
                            <?php if ($edit_professor): ?>
                                <input type="hidden" name="id" value="<?php echo $edit_professor['id']; ?>">
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم الأستاذ</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo $edit_professor ? $edit_professor['name'] : ''; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo $edit_professor ? $edit_professor['email'] : ''; ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo $edit_professor ? $edit_professor['phone'] : ''; ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="department" class="form-label">القسم</label>
                                <input type="text" class="form-control" id="department" name="department" 
                                       value="<?php echo $edit_professor ? $edit_professor['department'] : ''; ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="specialization" class="form-label">التخصص</label>
                                <input type="text" class="form-control" id="specialization" name="specialization" 
                                       value="<?php echo $edit_professor ? $edit_professor['specialization'] : ''; ?>">
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    <?php echo $edit_professor ? 'تحديث البيانات' : 'إضافة الأستاذ'; ?>
                                </button>
                                <?php if ($edit_professor): ?>
                                    <a href="professors.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة الأساتذة -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الأساتذة (<?php echo count($professors); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($professors)): ?>
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle me-2"></i>
                                لا يوجد أساتذة مسجلون
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الاسم</th>
                                            <th>القسم</th>
                                            <th>التخصص</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($professors as $professor): ?>
                                            <tr>
                                                <td><strong><?php echo $professor['name']; ?></strong></td>
                                                <td><?php echo $professor['department'] ?: '-'; ?></td>
                                                <td><?php echo $professor['specialization'] ?: '-'; ?></td>
                                                <td>
                                                    <?php if (!empty($professor['email'])): ?>
                                                        <a href="mailto:<?php echo $professor['email']; ?>" class="text-decoration-none">
                                                            <?php echo $professor['email']; ?>
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="professors.php?edit=<?php echo $professor['id']; ?>" class="btn btn-sm btn-warning me-1">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger me-1" 
                                                            onclick="confirmDelete(<?php echo $professor['id']; ?>, '<?php echo $professor['name']; ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-info" 
                                                            onclick="showDetails(<?php echo $professor['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج حذف الأستاذ -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف الأستاذ <strong id="professorNameToDelete"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" id="professorIdToDelete">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج عرض تفاصيل الأستاذ -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">تفاصيل الأستاذ</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="professorDetails">
                    <!-- سيتم ملء التفاصيل هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(id, name) {
            document.getElementById('professorIdToDelete').value = id;
            document.getElementById('professorNameToDelete').textContent = name;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function showDetails(id) {
            // جلب تفاصيل الأستاذ
            fetch('get_professor_details.php?id=' + id)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('professorDetails').innerHTML = data;
                    new bootstrap.Modal(document.getElementById('detailsModal')).show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في جلب التفاصيل');
                });
        }
    </script>
</body>
</html>
