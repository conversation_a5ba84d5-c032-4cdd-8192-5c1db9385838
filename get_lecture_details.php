<?php
require_once 'config.php';

if (isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    
    // جلب تفاصيل المحاضرة
    $query = "SELECT l.*, s.name as subject_name, s.code as subject_code, s.credit_hours,
                     p.name as professor_name, p.email as professor_email, p.department as professor_department,
                     h.name as hall_name, h.capacity as hall_capacity, h.location as hall_location, h.equipment
              FROM lectures l
              JOIN subjects s ON l.subject_id = s.id
              JOIN professors p ON l.professor_id = p.id
              JOIN halls h ON l.hall_id = h.id
              WHERE l.id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$id]);
    $lecture = $stmt->fetch();
    
    if ($lecture) {
        echo '<div class="row">';
        echo '<div class="col-md-6">';
        echo '<h6 class="text-primary">معلومات المحاضرة</h6>';
        echo '<table class="table table-sm">';
        echo '<tr><td><strong>عنوان المحاضرة:</strong></td><td>' . $lecture['title'] . '</td></tr>';
        echo '<tr><td><strong>المادة:</strong></td><td>' . $lecture['subject_name'] . ' (' . $lecture['subject_code'] . ')</td></tr>';
        echo '<tr><td><strong>الأستاذ:</strong></td><td>' . $lecture['professor_name'] . '</td></tr>';
        echo '<tr><td><strong>القاعة:</strong></td><td>' . $lecture['hall_name'] . '</td></tr>';
        echo '<tr><td><strong>اليوم:</strong></td><td>' . $lecture['day_of_week'] . '</td></tr>';
        echo '<tr><td><strong>الوقت:</strong></td><td>' . format_time($lecture['start_time']) . ' - ' . format_time($lecture['end_time']) . '</td></tr>';
        echo '<tr><td><strong>التاريخ:</strong></td><td>' . format_date($lecture['lecture_date']) . '</td></tr>';
        echo '<tr><td><strong>الفصل الدراسي:</strong></td><td>' . $lecture['semester'] . '</td></tr>';
        echo '<tr><td><strong>السنة الأكاديمية:</strong></td><td>' . $lecture['academic_year'] . '</td></tr>';
        echo '<tr><td><strong>الحالة:</strong></td><td>';
        
        $status_class = '';
        switch ($lecture['status']) {
            case 'مجدولة':
                $status_class = 'bg-success';
                break;
            case 'ملغية':
                $status_class = 'bg-danger';
                break;
            case 'مؤجلة':
                $status_class = 'bg-warning';
                break;
            case 'منتهية':
                $status_class = 'bg-secondary';
                break;
        }
        echo '<span class="badge ' . $status_class . '">' . $lecture['status'] . '</span>';
        echo '</td></tr>';
        echo '</table>';
        
        if (!empty($lecture['notes'])) {
            echo '<h6 class="text-primary mt-3">الملاحظات</h6>';
            echo '<p class="text-muted">' . nl2br($lecture['notes']) . '</p>';
        }
        
        echo '</div>';
        echo '<div class="col-md-6">';
        
        echo '<h6 class="text-primary">تفاصيل المادة</h6>';
        echo '<table class="table table-sm">';
        echo '<tr><td><strong>اسم المادة:</strong></td><td>' . $lecture['subject_name'] . '</td></tr>';
        echo '<tr><td><strong>رمز المادة:</strong></td><td>' . $lecture['subject_code'] . '</td></tr>';
        echo '<tr><td><strong>الساعات المعتمدة:</strong></td><td>' . $lecture['credit_hours'] . ' ساعة</td></tr>';
        echo '</table>';
        
        echo '<h6 class="text-primary mt-3">تفاصيل الأستاذ</h6>';
        echo '<table class="table table-sm">';
        echo '<tr><td><strong>الاسم:</strong></td><td>' . $lecture['professor_name'] . '</td></tr>';
        echo '<tr><td><strong>القسم:</strong></td><td>' . ($lecture['professor_department'] ?: '-') . '</td></tr>';
        if (!empty($lecture['professor_email'])) {
            echo '<tr><td><strong>البريد الإلكتروني:</strong></td><td><a href="mailto:' . $lecture['professor_email'] . '">' . $lecture['professor_email'] . '</a></td></tr>';
        }
        echo '</table>';
        
        echo '<h6 class="text-primary mt-3">تفاصيل القاعة</h6>';
        echo '<table class="table table-sm">';
        echo '<tr><td><strong>اسم القاعة:</strong></td><td>' . $lecture['hall_name'] . '</td></tr>';
        echo '<tr><td><strong>السعة:</strong></td><td>' . $lecture['hall_capacity'] . ' طالب</td></tr>';
        echo '<tr><td><strong>الموقع:</strong></td><td>' . ($lecture['hall_location'] ?: '-') . '</td></tr>';
        if (!empty($lecture['equipment'])) {
            echo '<tr><td><strong>المعدات:</strong></td><td>' . $lecture['equipment'] . '</td></tr>';
        }
        echo '</table>';
        
        echo '</div>';
        echo '</div>';
        
    } else {
        echo '<div class="alert alert-danger">المحاضرة غير موجودة</div>';
    }
} else {
    echo '<div class="alert alert-danger">معرف المحاضرة مطلوب</div>';
}
?>
