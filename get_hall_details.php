<?php
require_once 'config.php';

if (isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    
    // جلب تفاصيل القاعة
    $query = "SELECT * FROM halls WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$id]);
    $hall = $stmt->fetch();
    
    if ($hall) {
        // جلب المحاضرات المرتبطة بالقاعة
        $lectures_query = "SELECT l.*, s.name as subject_name, p.name as professor_name
                          FROM lectures l
                          JOIN subjects s ON l.subject_id = s.id
                          JOIN professors p ON l.professor_id = p.id
                          WHERE l.hall_id = ? AND l.status = 'مجدولة'
                          ORDER BY l.day_of_week, l.start_time";
        $lectures_stmt = $db->prepare($lectures_query);
        $lectures_stmt->execute([$id]);
        $lectures = $lectures_stmt->fetchAll();
        
        echo '<div class="row">';
        echo '<div class="col-md-6">';
        echo '<h6 class="text-primary">معلومات القاعة</h6>';
        echo '<table class="table table-sm">';
        echo '<tr><td><strong>اسم القاعة:</strong></td><td>' . $hall['name'] . '</td></tr>';
        echo '<tr><td><strong>السعة:</strong></td><td>' . $hall['capacity'] . ' طالب</td></tr>';
        echo '<tr><td><strong>الموقع:</strong></td><td>' . ($hall['location'] ?: '-') . '</td></tr>';
        echo '<tr><td><strong>الحالة:</strong></td><td>';
        
        $status_class = '';
        switch ($hall['status']) {
            case 'متاحة':
                $status_class = 'bg-success';
                break;
            case 'غير متاحة':
                $status_class = 'bg-danger';
                break;
            case 'صيانة':
                $status_class = 'bg-warning';
                break;
        }
        echo '<span class="badge ' . $status_class . '">' . $hall['status'] . '</span>';
        echo '</td></tr>';
        echo '<tr><td><strong>تاريخ الإنشاء:</strong></td><td>' . date('Y-m-d H:i', strtotime($hall['created_at'])) . '</td></tr>';
        echo '</table>';
        
        if (!empty($hall['equipment'])) {
            echo '<h6 class="text-primary mt-3">المعدات المتوفرة</h6>';
            echo '<p class="text-muted">' . nl2br($hall['equipment']) . '</p>';
        }
        
        echo '</div>';
        echo '<div class="col-md-6">';
        echo '<h6 class="text-primary">المحاضرات المجدولة (' . count($lectures) . ')</h6>';
        
        if (empty($lectures)) {
            echo '<div class="alert alert-info">لا توجد محاضرات مجدولة في هذه القاعة</div>';
        } else {
            echo '<div class="table-responsive">';
            echo '<table class="table table-sm table-striped">';
            echo '<thead><tr><th>اليوم</th><th>الوقت</th><th>المادة</th><th>الأستاذ</th></tr></thead>';
            echo '<tbody>';
            
            foreach ($lectures as $lecture) {
                echo '<tr>';
                echo '<td><span class="badge bg-secondary">' . $lecture['day_of_week'] . '</span></td>';
                echo '<td>' . format_time($lecture['start_time']) . ' - ' . format_time($lecture['end_time']) . '</td>';
                echo '<td>' . $lecture['subject_name'] . '</td>';
                echo '<td>' . $lecture['professor_name'] . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
            echo '</div>';
        }
        
        echo '</div>';
        echo '</div>';
        
    } else {
        echo '<div class="alert alert-danger">القاعة غير موجودة</div>';
    }
} else {
    echo '<div class="alert alert-danger">معرف القاعة مطلوب</div>';
}
?>
