<?php
require_once 'config.php';

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = sanitize_input($_POST['name']);
                $capacity = (int)$_POST['capacity'];
                $location = sanitize_input($_POST['location']);
                $equipment = sanitize_input($_POST['equipment']);
                $status = sanitize_input($_POST['status']);
                
                $query = "INSERT INTO halls (name, capacity, location, equipment, status) VALUES (?, ?, ?, ?, ?)";
                $stmt = $db->prepare($query);
                
                if ($stmt->execute([$name, $capacity, $location, $equipment, $status])) {
                    $message = "تم إضافة القاعة بنجاح";
                    $message_type = 'success';
                } else {
                    $message = "حدث خطأ في إضافة القاعة";
                    $message_type = 'error';
                }
                break;
                
            case 'edit':
                $id = (int)$_POST['id'];
                $name = sanitize_input($_POST['name']);
                $capacity = (int)$_POST['capacity'];
                $location = sanitize_input($_POST['location']);
                $equipment = sanitize_input($_POST['equipment']);
                $status = sanitize_input($_POST['status']);
                
                $query = "UPDATE halls SET name = ?, capacity = ?, location = ?, equipment = ?, status = ? WHERE id = ?";
                $stmt = $db->prepare($query);
                
                if ($stmt->execute([$name, $capacity, $location, $equipment, $status, $id])) {
                    $message = "تم تحديث القاعة بنجاح";
                    $message_type = 'success';
                } else {
                    $message = "حدث خطأ في تحديث القاعة";
                    $message_type = 'error';
                }
                break;
                
            case 'delete':
                $id = (int)$_POST['id'];
                
                // التحقق من وجود محاضرات مرتبطة بالقاعة
                $check_query = "SELECT COUNT(*) FROM lectures WHERE hall_id = ?";
                $check_stmt = $db->prepare($check_query);
                $check_stmt->execute([$id]);
                $lecture_count = $check_stmt->fetchColumn();
                
                if ($lecture_count > 0) {
                    $message = "لا يمكن حذف القاعة لأنها مرتبطة بـ $lecture_count محاضرة";
                    $message_type = 'error';
                } else {
                    $query = "DELETE FROM halls WHERE id = ?";
                    $stmt = $db->prepare($query);
                    
                    if ($stmt->execute([$id])) {
                        $message = "تم حذف القاعة بنجاح";
                        $message_type = 'success';
                    } else {
                        $message = "حدث خطأ في حذف القاعة";
                        $message_type = 'error';
                    }
                }
                break;
        }
    }
}

// جلب جميع القاعات
$query = "SELECT * FROM halls ORDER BY name ASC";
$stmt = $db->prepare($query);
$stmt->execute();
$halls = $stmt->fetchAll();

// جلب بيانات القاعة للتعديل
$edit_hall = null;
if (isset($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    $query = "SELECT * FROM halls WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$edit_id]);
    $edit_hall = $stmt->fetch();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة القاعات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calendar-alt me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="lectures.php">
                            <i class="fas fa-chalkboard-teacher me-1"></i>المحاضرات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="halls.php">
                            <i class="fas fa-building me-1"></i>القاعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="professors.php">
                            <i class="fas fa-user-tie me-1"></i>الأساتذة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-1"></i>المواد
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <?php echo show_message($message, $message_type); ?>
        <?php endif; ?>

        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center">
                    <i class="fas fa-building text-primary me-2"></i>
                    إدارة القاعات
                </h1>
            </div>
        </div>

        <div class="row">
            <!-- نموذج إضافة/تعديل القاعة -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-plus me-2"></i>
                            <?php echo $edit_hall ? 'تعديل القاعة' : 'إضافة قاعة جديدة'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="<?php echo $edit_hall ? 'edit' : 'add'; ?>">
                            <?php if ($edit_hall): ?>
                                <input type="hidden" name="id" value="<?php echo $edit_hall['id']; ?>">
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم القاعة</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo $edit_hall ? $edit_hall['name'] : ''; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="capacity" class="form-label">السعة</label>
                                <input type="number" class="form-control" id="capacity" name="capacity" 
                                       value="<?php echo $edit_hall ? $edit_hall['capacity'] : ''; ?>" min="1" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="location" class="form-label">الموقع</label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       value="<?php echo $edit_hall ? $edit_hall['location'] : ''; ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="equipment" class="form-label">المعدات</label>
                                <textarea class="form-control" id="equipment" name="equipment" rows="3"><?php echo $edit_hall ? $edit_hall['equipment'] : ''; ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="متاحة" <?php echo ($edit_hall && $edit_hall['status'] == 'متاحة') ? 'selected' : ''; ?>>متاحة</option>
                                    <option value="غير متاحة" <?php echo ($edit_hall && $edit_hall['status'] == 'غير متاحة') ? 'selected' : ''; ?>>غير متاحة</option>
                                    <option value="صيانة" <?php echo ($edit_hall && $edit_hall['status'] == 'صيانة') ? 'selected' : ''; ?>>صيانة</option>
                                </select>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    <?php echo $edit_hall ? 'تحديث القاعة' : 'إضافة القاعة'; ?>
                                </button>
                                <?php if ($edit_hall): ?>
                                    <a href="halls.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة القاعات -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة القاعات (<?php echo count($halls); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($halls)): ?>
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد قاعات مسجلة
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>اسم القاعة</th>
                                            <th>السعة</th>
                                            <th>الموقع</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($halls as $hall): ?>
                                            <tr>
                                                <td><strong><?php echo $hall['name']; ?></strong></td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $hall['capacity']; ?> طالب</span>
                                                </td>
                                                <td><?php echo $hall['location'] ?: '-'; ?></td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch ($hall['status']) {
                                                        case 'متاحة':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'غير متاحة':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                        case 'صيانة':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>"><?php echo $hall['status']; ?></span>
                                                </td>
                                                <td>
                                                    <a href="halls.php?edit=<?php echo $hall['id']; ?>" class="btn btn-sm btn-warning me-1">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" 
                                                            onclick="confirmDelete(<?php echo $hall['id']; ?>, '<?php echo $hall['name']; ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-info" 
                                                            onclick="showDetails(<?php echo $hall['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج حذف القاعة -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف القاعة <strong id="hallNameToDelete"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" id="hallIdToDelete">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج عرض تفاصيل القاعة -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">تفاصيل القاعة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="hallDetails">
                    <!-- سيتم ملء التفاصيل هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(id, name) {
            document.getElementById('hallIdToDelete').value = id;
            document.getElementById('hallNameToDelete').textContent = name;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function showDetails(id) {
            // جلب تفاصيل القاعة
            fetch('get_hall_details.php?id=' + id)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('hallDetails').innerHTML = data;
                    new bootstrap.Modal(document.getElementById('detailsModal')).show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في جلب التفاصيل');
                });
        }
    </script>
</body>
</html>
