/* تصميم عام للنظام */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
    text-align: right;
}

/* تصميم شريط التنقل */
.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

/* تصميم البطاقات */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: bold;
}

/* تصميم الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody td {
    vertical-align: middle;
    text-align: center;
    border-color: #e9ecef;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 123, 255, 0.05);
}

/* تصميم الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}

/* تصميم الشارات */
.badge {
    font-size: 0.85rem;
    padding: 6px 12px;
    border-radius: 20px;
}

/* تصميم التنبيهات */
.alert {
    border: none;
    border-radius: 10px;
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 1rem 1rem;
}

/* تصميم النماذج */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* تصميم بطاقات الإحصائيات */
.card.bg-primary, .card.bg-success, .card.bg-info, .card.bg-warning {
    border: none;
    background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark)) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, var(--bs-success), #157347) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, var(--bs-info), #087990) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--bs-warning), #997404) !important;
}

/* تصميم التذييل */
footer {
    margin-top: auto;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
        margin-bottom: 10px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* تحسينات إضافية */
.text-primary {
    color: #0d6efd !important;
}

.bg-light-custom {
    background-color: #f8f9fa !important;
}

.border-custom {
    border: 2px solid #e9ecef !important;
}

.shadow-custom {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* تصميم خاص للجدولة */
.schedule-time {
    font-weight: bold;
    color: #0d6efd;
}

.schedule-subject {
    font-weight: 600;
    color: #198754;
}

.schedule-hall {
    background-color: #e7f3ff;
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 0.9rem;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.card, .alert {
    animation: fadeIn 0.5s ease-out;
}

/* تصميم الأيقونات */
.fas, .far {
    margin-left: 5px;
}

/* تحسين عرض النصوص العربية */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #2c3e50;
}

.text-muted {
    color: #6c757d !important;
}
