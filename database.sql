-- إن<PERSON>ا<PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS lecture_hall_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE lecture_hall_system;

-- جدول القاعات
CREATE TABLE IF NOT EXISTS halls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    capacity INT NOT NULL,
    location VARCHAR(200),
    equipment TEXT,
    status ENUM('متاحة', 'غير متاحة', 'صيانة') DEFAULT 'متاحة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الأساتذة
CREATE TABLE IF NOT EXISTS professors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    phone VARCHAR(20),
    department VARCHAR(100),
    specialization VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المواد الدراسية
CREATE TABLE IF NOT EXISTS subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    credit_hours INT DEFAULT 3,
    department VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المحاضرات
CREATE TABLE IF NOT EXISTS lectures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_id INT NOT NULL,
    professor_id INT NOT NULL,
    hall_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    lecture_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    day_of_week ENUM('الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت') NOT NULL,
    semester ENUM('الفصل الأول', 'الفصل الثاني', 'الفصل الصيفي') NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    notes TEXT,
    status ENUM('مجدولة', 'ملغية', 'مؤجلة', 'منتهية') DEFAULT 'مجدولة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (professor_id) REFERENCES professors(id) ON DELETE CASCADE,
    FOREIGN KEY (hall_id) REFERENCES halls(id) ON DELETE CASCADE
);

-- جدول الطلاب (اختياري)
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    department VARCHAR(100),
    level ENUM('السنة الأولى', 'السنة الثانية', 'السنة الثالثة', 'السنة الرابعة') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول تسجيل الطلاب في المحاضرات
CREATE TABLE IF NOT EXISTS lecture_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lecture_id INT NOT NULL,
    student_id INT NOT NULL,
    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    attendance_status ENUM('حاضر', 'غائب', 'متأخر') DEFAULT 'حاضر',
    FOREIGN KEY (lecture_id) REFERENCES lectures(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    UNIQUE KEY unique_enrollment (lecture_id, student_id)
);

-- إدراج بيانات تجريبية للقاعات
INSERT INTO halls (name, capacity, location, equipment, status) VALUES
('قاعة A101', 50, 'الطابق الأول - مبنى A', 'بروجكتر، سبورة ذكية، مكيف', 'متاحة'),
('قاعة A102', 30, 'الطابق الأول - مبنى A', 'بروجكتر، سبورة عادية', 'متاحة'),
('قاعة B201', 80, 'الطابق الثاني - مبنى B', 'بروجكتر، سبورة ذكية، مكيف، ميكروفون', 'متاحة'),
('قاعة B202', 40, 'الطابق الثاني - مبنى B', 'بروجكتر، سبورة عادية', 'متاحة'),
('مختبر الحاسوب 1', 25, 'الطابق الأول - مبنى C', '25 جهاز حاسوب، بروجكتر', 'متاحة');

-- إدراج بيانات تجريبية للأساتذة
INSERT INTO professors (name, email, phone, department, specialization) VALUES
('د. أحمد محمد', '<EMAIL>', '01234567890', 'علوم الحاسوب', 'هندسة البرمجيات'),
('د. فاطمة علي', '<EMAIL>', '01234567891', 'الرياضيات', 'الجبر الخطي'),
('د. محمد حسن', '<EMAIL>', '01234567892', 'الفيزياء', 'الفيزياء النظرية'),
('د. سارة أحمد', '<EMAIL>', '01234567893', 'الكيمياء', 'الكيمياء العضوية'),
('د. عمر خالد', '<EMAIL>', '01234567894', 'علوم الحاسوب', 'قواعد البيانات');

-- إدراج بيانات تجريبية للمواد
INSERT INTO subjects (name, code, credit_hours, department, description) VALUES
('برمجة الحاسوب 1', 'CS101', 3, 'علوم الحاسوب', 'مقدمة في البرمجة باستخدام لغة C++'),
('الرياضيات المتقدمة', 'MATH201', 4, 'الرياضيات', 'التفاضل والتكامل المتقدم'),
('الفيزياء العامة', 'PHYS101', 3, 'الفيزياء', 'مبادئ الفيزياء الأساسية'),
('الكيمياء العامة', 'CHEM101', 3, 'الكيمياء', 'أساسيات الكيمياء'),
('قواعد البيانات', 'CS301', 3, 'علوم الحاسوب', 'تصميم وإدارة قواعد البيانات');

-- إدراج بيانات تجريبية للمحاضرات
INSERT INTO lectures (subject_id, professor_id, hall_id, title, lecture_date, start_time, end_time, day_of_week, semester, academic_year, notes) VALUES
(1, 1, 1, 'مقدمة في البرمجة', '2024-01-15', '08:00:00', '09:30:00', 'الأحد', 'الفصل الثاني', '2023-2024', 'محاضرة تمهيدية'),
(2, 2, 2, 'التفاضل والتكامل', '2024-01-15', '10:00:00', '11:30:00', 'الأحد', 'الفصل الثاني', '2023-2024', ''),
(3, 3, 3, 'قوانين نيوتن', '2024-01-16', '08:00:00', '09:30:00', 'الاثنين', 'الفصل الثاني', '2023-2024', 'يرجى إحضار الآلة الحاسبة'),
(4, 4, 4, 'الجدول الدوري', '2024-01-16', '10:00:00', '11:30:00', 'الاثنين', 'الفصل الثاني', '2023-2024', ''),
(5, 5, 5, 'مقدمة في قواعد البيانات', '2024-01-17', '08:00:00', '09:30:00', 'الثلاثاء', 'الفصل الثاني', '2023-2024', 'محاضرة في مختبر الحاسوب');
