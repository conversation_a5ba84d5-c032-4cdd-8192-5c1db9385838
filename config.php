<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'lecture_hall_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SITE_NAME', 'نظام جدولة القاعات والمحاضرات');
define('SITE_URL', 'http://localhost/uni');

// إعدادات التاريخ والوقت
date_default_timezone_set('Asia/Riyadh');

// كلاس الاتصال بقاعدة البيانات
class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    // الاتصال بقاعدة البيانات
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}

// دوال مساعدة
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function redirect($url) {
    header("Location: " . $url);
    exit();
}

function show_message($message, $type = 'info') {
    $class = '';
    switch($type) {
        case 'success':
            $class = 'alert-success';
            break;
        case 'error':
            $class = 'alert-danger';
            break;
        case 'warning':
            $class = 'alert-warning';
            break;
        default:
            $class = 'alert-info';
    }
    
    return '<div class="alert ' . $class . ' alert-dismissible fade show" role="alert">
                ' . $message . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>';
}

function format_date($date) {
    return date('Y-m-d', strtotime($date));
}

function format_time($time) {
    return date('H:i', strtotime($time));
}

function get_day_name($day) {
    $days = [
        'Sunday' => 'الأحد',
        'Monday' => 'الاثنين',
        'Tuesday' => 'الثلاثاء',
        'Wednesday' => 'الأربعاء',
        'Thursday' => 'الخميس',
        'Friday' => 'الجمعة',
        'Saturday' => 'السبت'
    ];
    
    return isset($days[$day]) ? $days[$day] : $day;
}

// بدء الجلسة
session_start();

// إنشاء اتصال قاعدة البيانات العام
$database = new Database();
$db = $database->getConnection();

// التحقق من الاتصال
if (!$db) {
    die("فشل في الاتصال بقاعدة البيانات");
}
?>
